﻿﻿using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels.NormalWorkingReminder;
using Xunit;
using Xunit.DependencyInjection;

namespace NursingManagement.UnitTest
{
    /// <summary>
    /// 常态工作控制提醒服务单元测试
    /// </summary>
    public class NormalWorkingReminderServiceTest
    {
        private readonly INormalWorkingReminderService _normalWorkingReminderService;
        private readonly ITestOutputHelperAccessor _testOutputHelperAccessor;

        public NormalWorkingReminderServiceTest(
            INormalWorkingReminderService normalWorkingReminderService,
            ITestOutputHelperAccessor testOutputHelperAccessor)
        {
            _normalWorkingReminderService = normalWorkingReminderService;
            _testOutputHelperAccessor = testOutputHelperAccessor;
        }

        /// <summary>
        /// 测试执行提醒 - 参数验证
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithNullRequest_ShouldReturnError()
        {
            // Arrange
            ReminderRequestView request = null;

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
        }

        /// <summary>
        /// 测试执行提醒 - 无效提醒类型
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithInvalidReminderType_ShouldReturnError()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 999 // 无效的提醒类型
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("不支持的提醒类型", result.Message);
        }

        /// <summary>
        /// 测试执行提醒 - 空医院ID（应该从配置获取）
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithEmptyHospitalID_ShouldUseConfigValue()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "", // 空值，应该从配置获取
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为空HospitalID会从配置获取
        }

        /// <summary>
        /// 测试执行提醒 - 不支持的提醒类型
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithInvalidReminderType_ShouldReturnFailure()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 999 // 不支持的类型
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("不支持的提醒类型", result.Message);
        }

        /// <summary>
        /// 测试执行三天提醒
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithThreeDayType_ShouldReturnResult()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为可能没有需要提醒的问题
        }

        /// <summary>
        /// 测试执行六天提醒
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithSixDayType_ShouldReturnResult()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 6
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为可能没有需要提醒的问题
        }

        /// <summary>
        /// 测试HospitalID为 null 时从配置获取
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithNullHospitalID_ShouldUseConfigValue()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = null, // 空值，应该从配置获取
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为依赖于配置中是否有HospitalID
        }
    }
}
