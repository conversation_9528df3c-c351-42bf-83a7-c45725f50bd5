﻿using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCMainRepository
    {
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainInfo>> GetDataByRecordIDs(List<string> recordIDs);

        /// <summary>
        /// 根据维护记录ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<HierarchicalQCMainInfo> GetDataByMainID(string mainID);

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainInfo>> GetDataByRecordID(string recordID);

        /// <summary>
        /// 根据主记录ID获取最后一条数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainInfo>> GetLastDataByRecordIDs(List<string> recordIDs);

        /// <summary>
        /// 根据主记录ID获取最后一条数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<HierarchicalQCMainInfo> GetLastDataByRecordID(string recordID);

        /// <summary>
        /// 获取质控模板码
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<string> GetTemplateCodeByMainIDAsync(string mainID);

        /// <summary>
        /// 根据主键集合获取质控维护记录
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainInfo>> GetDataByMainIDsAsync(List<string> mainIDs);

        /// <summary>
        /// 获取评价和指导内容
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        Task<Tuple<string, string>> GetGuidanceAndImprovement(string careMainID);

        /// <summary>
        /// 获取考核主记录ID
        /// </summary>
        /// <param name="mainID">维护记录ID</param>
        /// <returns></returns>
        Task<string> GetQcRecordIDByMainID(string mainID);

        /// <summary>
        /// 根据主记录ID获取审批用view
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        Task<List<HQcMainApproveView>> GetQcMainViewsByRecordID(string recordID);

        /// <summary>
        /// 获取主记录阅读状态
        /// </summary>
        /// <param name="hierarchicalQCMainID">质控主键ID</param>
        /// <returns></returns>
        Task<Dictionary<string, bool?>> GetQCMainIsReadStatus(List<string> hierarchicalQCMainIDs);

        /// <summary>
        /// 获取主记录阅读状态
        /// </summary>
        /// <param name="hierarchicalQCRecordIDs">质控主键ID</param>
        /// <returns></returns>
        Task<Dictionary<string, bool>> GetQCRecordIsReadStatus(List<string> hierarchicalQCRecordIDs);

        /// <summary>
        /// 根据考核日期获取指定天数前的质控维护记录
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="daysAgo">多少天前</param>
        /// <param name="formType">质控模板类型</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>质控维护记录列表</returns>
        Task<List<HierarchicalQCMainInfo>> GetMainRecordsByDaysAgoAndFormTypeAsNoTrackAsync(string hospitalID, int daysAgo, string formType, int? departmentID = null);

        /// <summary>
        /// 根据修改人员ID和时间范围批量查询质控记录
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="employeeIDs">修改人员ID列表</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>质控记录列表</returns>
        Task<List<HierarchicalQCMainInfo>> GetQCRecordsByEmployeeIDsAndDateRangeAsync(string hospitalID, List<string> employeeIDs, DateTime startDate, DateTime endDate);
    }
}
