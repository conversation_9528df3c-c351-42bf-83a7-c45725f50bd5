﻿﻿namespace NursingManagement.ViewModels.NormalWorkingReminder
{
    /// <summary>
    /// 提醒执行结果视图
    /// </summary>
    public class ReminderResultView
    {
        /// <summary>
        /// 执行是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 执行消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 需要提醒的问题数量
        /// </summary>
        public int ProblemsNeedReminder { get; set; }

        /// <summary>
        /// 成功发送提醒的数量
        /// </summary>
        public int SuccessfulReminders { get; set; }

        /// <summary>
        /// 失败的提醒数量
        /// </summary>
        public int FailedReminders { get; set; }

        /// <summary>
        /// 提醒详情列表
        /// </summary>
        public List<ReminderDetailView> ReminderDetails { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        public ReminderResultView()
        {
            ReminderDetails = new List<ReminderDetailView>();
            ExecutionTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 提醒详情视图
    /// </summary>
    public class ReminderDetailView
    {
        /// <summary>
        /// 质控维护记录ID
        /// </summary>
        public string HierarchicalQCMainID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 提醒类型：3-护士长提醒，6-片区主任提醒
        /// </summary>
        public int ReminderType { get; set; }

        /// <summary>
        /// 接收提醒的员工ID列表
        /// </summary>
        public List<string> ReceiverEmployeeIDs { get; set; }

        /// <summary>
        /// 提醒是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 提醒消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息（如果失败）
        /// </summary>
        public string ErrorMessage { get; set; }

        public ReminderDetailView()
        {
            ReceiverEmployeeIDs = new List<string>();
        }
    }
}
