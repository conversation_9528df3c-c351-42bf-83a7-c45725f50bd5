namespace NursingManagement.ViewModels.NormalWorkingReminder
{
    /// <summary>
    /// 管理层参与动态监测提醒执行结果
    /// </summary>
    public class ManagementParticipationReminderResult
    {
        /// <summary>
        /// 执行是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 执行消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 检查的护士长总数
        /// </summary>
        public int TotalHeadNurses { get; set; }

        /// <summary>
        /// 一周内无质控记录的护士长数量
        /// </summary>
        public int WeeklyReminderCount { get; set; }

        /// <summary>
        /// 连续两周无质控记录的护士长数量
        /// </summary>
        public int BiWeeklyReminderCount { get; set; }

        /// <summary>
        /// 当月25日前无质控记录的护士长数量
        /// </summary>
        public int MonthlyReminderCount { get; set; }

        /// <summary>
        /// 成功发送提醒的数量
        /// </summary>
        public int SuccessfulReminders { get; set; }

        /// <summary>
        /// 失败的提醒数量
        /// </summary>
        public int FailedReminders { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// 详细提醒信息
        /// </summary>
        public List<string> ReminderDetails { get; set; }

        public ManagementParticipationReminderResult()
        {
            Success = true;
            ExecutionTime = DateTime.Now;
            ReminderDetails = new List<string>();
        }
    }

    /// <summary>
    /// 护士长视图
    /// </summary>
    public class HeadNurseView
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
    }

    /// <summary>
    /// 质控记录视图
    /// </summary>
    public class QCRecordView
    {
        /// <summary>
        /// 修改人员ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 质控维护记录主键
        /// </summary>
        public string HierarchicalQCMainID { get; set; }

        /// <summary>
        /// 质控主记录主键
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
    }

    /// <summary>
    /// 时间范围视图
    /// </summary>
    public class QCTimeRangesView
    {
        /// <summary>
        /// 上周开始日期
        /// </summary>
        public DateTime LastWeekStart { get; set; }

        /// <summary>
        /// 上周结束日期
        /// </summary>
        public DateTime LastWeekEnd { get; set; }

        /// <summary>
        /// 两周前开始日期
        /// </summary>
        public DateTime TwoWeeksAgoStart { get; set; }

        /// <summary>
        /// 当月开始日期
        /// </summary>
        public DateTime MonthStart { get; set; }

        /// <summary>
        /// 当月25日
        /// </summary>
        public DateTime Month25th { get; set; }
    }

    /// <summary>
    /// 提醒收集数据视图
    /// </summary>
    public class ReminderCollectionDataView
    {
        /// <summary>
        /// 一周提醒列表
        /// </summary>
        public List<MessageReminderView> WeeklyReminders { get; set; } = [];

        /// <summary>
        /// 两周提醒列表
        /// </summary>
        public List<MessageReminderView> BiWeeklyReminders { get; set; } = [];

        /// <summary>
        /// 月度提醒列表
        /// </summary>
        public List<MessageReminderView> MonthlyReminders { get; set; } = [];
    }

    /// <summary>
    /// 消息提醒视图
    /// </summary>
    public class MessageReminderView
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 提醒消息
        /// </summary>
        public string Message { get; set; }
    }
}
