﻿namespace NursingManagement.Data.Interface
{
    public interface IEmployeeToJobRepository : ICacheRepository
    {
        /// <summary>
        /// 根据职务编号获取工号
        /// </summary>
        /// <param name="jobCode">职务编号</param>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        Task<List<string>> GetEmployeeIDByJobCode(string jobCode, int departmentID);

        /// <summary>
        /// 根据科室ID获取职务编号
        /// </summary>
        /// <param name="departmentID">科室</param>
        /// <returns></returns>
        Task<HashSet<string>> GetJobCodesByDepartmentID(int departmentID);

        /// <summary>
        /// 获取同一部门相同岗位的人员工号
        /// </summary>
        /// <param name="jobCodes">岗位集合</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<HashSet<string>> GetEmployeeIDsByDepartmentIDAndJobCodesAsync(List<string> jobCodes, int departmentID);

        /// <summary>
        /// 根据职务编号获取护士长员工ID列表（DepartmentID为0）
        /// </summary>
        /// <param name="jobCode">职务编号</param>
        /// <returns>员工ID列表</returns>
        Task<List<string>> GetHeadNurseEmployeeIDsByJobCodeAsync(string jobCode);
    }
}
