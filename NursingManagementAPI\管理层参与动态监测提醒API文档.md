# 管理层参与动态监测提醒 API 文档

## 概述

本 API 提供管理层参与动态监测提醒功能，专为外部定时任务调用设计：

- 一周内无管理层质控记录提醒护士长本人
- 连续两周无管理层质控记录提醒片区行政主任
- 当月25日前无管理层质控记录提醒护理部
- 支持微信和电脑端同步提醒

## API 接口

### 执行管理层参与动态监测提醒

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteManagementParticipationReminder`

**请求参数：** 无需参数

**功能说明：** 
- 自动查找所有护士长（JobCode=975，DepartmentID为空）
- 检查护士长在指定时间范围内的质控记录
- 根据缺失情况发送相应提醒

**提醒规则：**
1. **一周内无质控记录**：提醒护士长本人
   - 提醒内容：`"提醒：上周（X月X日至X月X日）还未有您的质控记录"`
   - 接收人：护士长本人

2. **连续两周无质控记录**：提醒片区行政主任
   - 提醒内容：`"XXX已连续两周无常态工作过程控制记录，请关注！"`
   - 接收人：护士长所属片区的行政主任

3. **当月25日前无质控记录**：提醒护理部
   - 提醒内容：`"XXX目前无常态工作过程控制记录，请关注！"`
   - 接收人：护理部（李丹慧）
   - 执行条件：仅在每月25日及以后执行检查

## 响应格式

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "发送完成，成功5条，失败0条",
    "totalHeadNurses": 20,
    "weeklyReminderCount": 3,
    "biWeeklyReminderCount": 1,
    "monthlyReminderCount": 1,
    "successfulReminders": 5,
    "failedReminders": 0,
    "executionTime": "2024-06-30T10:30:00",
    "reminderDetails": [
      "一周提醒：张三 - 成功",
      "一周提醒：李四 - 成功",
      "两周提醒：王五 -> 片区主任 - 成功",
      "月度提醒：赵六 -> 护理部 - 成功"
    ]
  }
}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 执行是否成功 |
| message | string | 执行消息 |
| totalHeadNurses | int | 检查的护士长总数 |
| weeklyReminderCount | int | 一周内无质控记录的护士长数量 |
| biWeeklyReminderCount | int | 连续两周无质控记录的护士长数量 |
| monthlyReminderCount | int | 当月25日前无质控记录的护士长数量 |
| successfulReminders | int | 成功发送提醒的数量 |
| failedReminders | int | 失败的提醒数量 |
| executionTime | datetime | 执行时间 |
| reminderDetails | array | 详细提醒信息列表 |

## 技术实现

### 数据查询优化
- 批量查询：避免在循环中频繁查询数据库
- 一次性获取所有护士长的质控记录
- 在内存中进行过滤和判断

### 时间范围计算
- **自然周**：周一到周日为一个完整周期
- **上周**：当前周的前一周
- **连续两周**：上周和上两周都无记录
- **当月25日前**：当月1日到25日的时间范围

### 消息发送
- 支持MQ和微信双通道发送
- 统一批量发送，提高效率
- 详细的发送结果记录

## 调用示例

### cURL 示例
```bash
curl -X POST "http://localhost:5000/api/NormalWorkingReminder/ExecuteManagementParticipationReminder" \
     -H "Content-Type: application/json"
```

### PowerShell 示例
```powershell
Invoke-RestMethod -Uri "http://localhost:5000/api/NormalWorkingReminder/ExecuteManagementParticipationReminder" `
                  -Method Post `
                  -ContentType "application/json"
```

## 定时任务配置建议

建议配置定时任务在以下时间执行：
- **每周一上午9:00**：检查上周质控记录
- **每月26日上午9:00**：检查当月质控记录

## 注意事项

1. **护士长识别**：基于 EmployeeToJob 表中 JobCode=975 且 DepartmentID=0 的记录
2. **质控记录判断**：基于 HierarchicalQCMainInfo 表的 ModifyEmployeeID 字段
3. **片区主任查找**：通过部门层级关系查找 OrganizationType=3 且 Level=1 的部门
4. **消息发送**：使用现有的消息服务，支持多种通知方式
5. **日志记录**：详细记录执行过程和结果，便于问题排查

## 错误处理

- 未找到护士长数据时返回相应错误信息
- 消息发送失败时记录失败详情
- 所有异常通过全局异常处理机制处理
