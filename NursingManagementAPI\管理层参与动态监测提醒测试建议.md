# 管理层参与动态监测提醒功能测试建议

## 测试概述

为确保管理层参与动态监测提醒功能的正确性和稳定性，建议进行以下测试：

## 1. 单元测试

### 1.1 Repository层测试

**测试文件：** `HierarchicalQCMainRepositoryTests.cs`

```csharp
[Test]
public async Task GetQCRecordViewsByEmployeeIDsAndDateRangeAsync_ShouldReturnCorrectRecords()
{
    // Arrange
    var hospitalID = "TEST_HOSPITAL";
    var employeeIDs = new List<string> { "EMP001", "EMP002" };
    var startDate = DateTime.Now.AddDays(-14);
    
    // Act
    var result = await _repository.GetQCRecordViewsByEmployeeIDsAndDateRangeAsync(
        hospitalID, employeeIDs, startDate);
    
    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.All(r => employeeIDs.Contains(r.ModifyEmployeeID)));
    Assert.IsTrue(result.All(r => r.ModifyDateTime >= startDate));
}
```

### 1.2 Service层测试

**测试文件：** `NormalWorkingReminderServiceTests.cs`

```csharp
[Test]
public async Task ExecuteManagementParticipationReminderAsync_ShouldReturnSuccessResult()
{
    // Arrange
    // Mock dependencies and setup test data
    
    // Act
    var result = await _service.ExecuteManagementParticipationReminderAsync();
    
    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Success);
    Assert.IsTrue(result.TotalHeadNurses >= 0);
}

[Test]
public void GetTimeRanges_ShouldCalculateCorrectWeekRanges()
{
    // Act
    var timeRanges = _service.GetTimeRanges();
    
    // Assert
    Assert.IsNotNull(timeRanges);
    Assert.IsTrue(timeRanges.LastWeekEnd > timeRanges.LastWeekStart);
    Assert.IsTrue(timeRanges.LastWeekStart > timeRanges.TwoWeeksAgoStart);
}
```

## 2. 集成测试

### 2.1 API接口测试

**测试文件：** `NormalWorkingReminderControllerIntegrationTests.cs`

```csharp
[Test]
public async Task ExecuteManagementParticipationReminder_ShouldReturnOkResult()
{
    // Act
    var response = await _client.PostAsync(
        "/api/NormalWorkingReminder/ExecuteManagementParticipationReminder", 
        null);
    
    // Assert
    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
    
    var content = await response.Content.ReadAsStringAsync();
    var result = JsonConvert.DeserializeObject<ResponseResult>(content);
    
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Code == 1); // Success
}
```

### 2.2 数据库集成测试

```csharp
[Test]
public async Task GetHeadNursesWithNames_ShouldReturnValidData()
{
    // Arrange
    // Setup test database with known data
    
    // Act
    var headNurses = await _service.GetHeadNursesWithNames();
    
    // Assert
    Assert.IsNotNull(headNurses);
    Assert.IsTrue(headNurses.All(h => !string.IsNullOrWhiteSpace(h.EmployeeID)));
    Assert.IsTrue(headNurses.All(h => !string.IsNullOrWhiteSpace(h.EmployeeName)));
}
```

## 3. 功能测试场景

### 3.1 一周提醒测试

**测试场景：** 护士长上周无质控记录

**测试步骤：**
1. 准备测试数据：创建护士长记录，确保上周无质控记录
2. 调用API接口
3. 验证返回结果中包含一周提醒
4. 验证消息内容格式正确
5. 验证消息发送给护士长本人

### 3.2 两周提醒测试

**测试场景：** 护士长连续两周无质控记录

**测试步骤：**
1. 准备测试数据：创建护士长记录，确保连续两周无质控记录
2. 设置片区行政主任数据
3. 调用API接口
4. 验证返回结果中包含两周提醒
5. 验证消息发送给片区行政主任

### 3.3 月度提醒测试

**测试场景：** 当月25日前无质控记录

**测试步骤：**
1. 设置系统时间为25日或之后
2. 准备测试数据：护士长当月1-25日无质控记录
3. 调用API接口
4. 验证返回结果中包含月度提醒
5. 验证消息发送给护理部

### 3.4 边界条件测试

**测试场景：**
- 无护士长数据
- 所有护士长都有质控记录
- 系统时间在25日之前（不应触发月度提醒）
- 片区行政主任不存在的情况

## 4. 性能测试

### 4.1 大数据量测试

**测试场景：** 100个护士长，1000条质控记录

**测试指标：**
- 响应时间 < 5秒
- 内存使用合理
- 数据库查询次数最小化

### 4.2 并发测试

**测试场景：** 多个定时任务同时调用

**测试指标：**
- 无数据竞争
- 结果一致性
- 系统稳定性

## 5. 测试数据准备

### 5.1 基础数据

```sql
-- 护士长数据
INSERT INTO EmployeeToJobInfos (EmployeeID, JobCode, DepartmentID, HospitalID, DeleteFlag)
VALUES 
('NURSE001', '975', 0, 'TEST_HOSPITAL', ''),
('NURSE002', '975', 0, 'TEST_HOSPITAL', '');

-- 员工个人信息
INSERT INTO EmployeePersonalDataInfos (EmployeeID, EmployeeName, HospitalID, DeleteFlag)
VALUES 
('NURSE001', '张护士长', 'TEST_HOSPITAL', ''),
('NURSE002', '李护士长', 'TEST_HOSPITAL', '');

-- 质控记录（用于测试有记录的情况）
INSERT INTO HierarchicalQCMainInfos (HierarchicalQCMainID, ModifyEmployeeID, ModifyDateTime, HospitalID, DeleteFlag)
VALUES 
('QC001', 'NURSE001', '2024-06-20 10:00:00', 'TEST_HOSPITAL', '');
```

### 5.2 时间相关测试数据

```csharp
// 计算测试时间范围
var now = DateTime.Now;
var lastWeekStart = now.AddDays(-7 - (int)now.DayOfWeek + 1);
var lastWeekEnd = lastWeekStart.AddDays(6);
var twoWeeksAgoStart = lastWeekStart.AddDays(-7);
```

## 6. 测试执行建议

### 6.1 自动化测试

- 使用NUnit或xUnit框架
- 集成到CI/CD流水线
- 每次代码提交自动执行

### 6.2 手动测试

- 在测试环境验证完整流程
- 验证消息发送功能
- 检查日志记录

### 6.3 测试环境要求

- 独立的测试数据库
- 模拟的消息服务
- 可控的系统时间

## 7. 测试检查清单

- [ ] Repository层方法正确查询数据
- [ ] Service层业务逻辑正确
- [ ] API接口返回正确格式
- [ ] 一周提醒功能正常
- [ ] 两周提醒功能正常
- [ ] 月度提醒功能正常
- [ ] 时间范围计算正确
- [ ] 消息发送功能正常
- [ ] 错误处理机制有效
- [ ] 性能指标达标
- [ ] 日志记录完整
- [ ] 边界条件处理正确

## 8. 问题排查指南

### 8.1 常见问题

1. **无护士长数据**
   - 检查JobCode=975的数据
   - 检查DepartmentID=0的条件
   - 检查DeleteFlag字段

2. **时间计算错误**
   - 验证自然周计算逻辑
   - 检查时区设置
   - 验证25日判断逻辑

3. **消息发送失败**
   - 检查消息服务配置
   - 验证员工ID有效性
   - 检查消息格式

### 8.2 调试建议

- 启用详细日志记录
- 使用断点调试关键逻辑
- 验证数据库查询结果
- 检查依赖注入配置

通过以上测试方案，可以确保管理层参与动态监测提醒功能的质量和稳定性。
