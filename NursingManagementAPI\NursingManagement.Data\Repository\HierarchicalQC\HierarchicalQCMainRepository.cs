﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCMainRepository : IHierarchicalQCMainRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public HierarchicalQCMainRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainInfo>> GetDataByRecordIDs(List<string> recordIDs)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => recordIDs.Any(n => n == m.HierarchicalQCRecordID) && m.DeleteFlag != "*").OrderBy(m => m.AddDateTime).ToListAsync();
        }

        /// <summary>
        /// 根据维护记录ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<HierarchicalQCMainInfo> GetDataByMainID(string mainID)
        {
            return await _dbContext.HierarchicalQCMainInfos.FirstOrDefaultAsync(m => m.HierarchicalQCMainID == mainID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainInfo>> GetDataByRecordID(string recordID)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => m.HierarchicalQCRecordID == recordID && m.DeleteFlag != "*").OrderBy(m => m.AddDateTime).ToListAsync();
        }

        /// <summary>
        /// 根据主记录ID获取最后一条数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainInfo>> GetLastDataByRecordIDs(List<string> recordIDs)
        {
            return await _dbContext.HierarchicalQCMainInfos
         .Where(m => recordIDs.Any(n => n == m.HierarchicalQCRecordID) && m.DeleteFlag != "*").GroupBy(m => m.HierarchicalQCRecordID).Select(g => g.OrderBy(m => m.ModifyDateTime).LastOrDefault()).ToListAsync();
        }

        /// <summary>
        /// 根据主记录ID获取最后一条数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<HierarchicalQCMainInfo> GetLastDataByRecordID(string recordID)
        {
            return await _dbContext.HierarchicalQCMainInfos
         .Where(m => m.HierarchicalQCRecordID == recordID && m.DeleteFlag != "*").GroupBy(m => m.HierarchicalQCRecordID).Select(g => g.OrderBy(m => m.AddDateTime).LastOrDefault()).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取质控模板码
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<string> GetTemplateCodeByMainIDAsync(string mainID)
        {
            return await (from m in _dbContext.HierarchicalQCMainInfos.Where(m => m.HierarchicalQCMainID == mainID && m.DeleteFlag != "*")
                          join n in _dbContext.HierarchicalQCFormInfos.Where(m => m.DeleteFlag != "*")
                           on m.HierarchicalQCFormID equals n.HierarchicalQCFormID
                          select n.TemplateCode).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据主键集合获取质控维护记录
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainInfo>> GetDataByMainIDsAsync(List<string> mainIDs)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => mainIDs.Contains(m.HierarchicalQCMainID) && m.DeleteFlag != "*").OrderBy(m => m.AddDateTime).ToListAsync();
        }

        /// <summary>
        /// 获取评价和指导内容
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns></returns>
        public async Task<Tuple<string, string>> GetGuidanceAndImprovement(string careMainID)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => m.HierarchicalQCMainID == careMainID && m.DeleteFlag != "*")
                .Select(m => Tuple.Create(m.Guidance, m.Improvement)).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取考核主记录ID
        /// </summary>
        /// <param name="mainID">维护记录ID</param>
        /// <returns></returns>
        public async Task<string> GetQcRecordIDByMainID(string mainID)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => m.HierarchicalQCMainID == mainID && m.DeleteFlag != "*")
                .Select(m => m.HierarchicalQCRecordID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据主记录ID获取审批用view
        /// </summary>
        /// <param name="recordID">主记录ID</param>
        /// <returns></returns>
        public async Task<List<HQcMainApproveView>> GetQcMainViewsByRecordID(string recordID)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => m.HierarchicalQCRecordID == recordID && m.DeleteFlag != "*")
                .OrderBy(m => m.AddDateTime)
                .Select(m => new HQcMainApproveView
                {
                    HQcMainId = m.HierarchicalQCMainID,
                    HQcDate = m.AssessDate,
                    HQcScore = m.Result,
                    AuditStatus = m.AuditStatus
                }).ToListAsync();
        }

        /// <summary>
        /// 获取主记录阅读状态
        /// </summary>
        /// <param name="hierarchicalQCMainIDs">质控主键ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, bool?>> GetQCMainIsReadStatus(List<string> hierarchicalQCMainIDs)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => hierarchicalQCMainIDs.Contains(m.HierarchicalQCMainID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.HierarchicalQCMainID, m => m.IsReadFlag);
        }

        /// <summary>
        /// 获取主记录阅读状态
        /// </summary>
        /// <param name="hierarchicalQCMainIDs">质控主键ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, bool>> GetQCRecordIsReadStatus(List<string> hierarchicalQCRecordIDs)
        {
            var result = new Dictionary<string, bool>();
            return await _dbContext.HierarchicalQCMainInfos.Where(m => hierarchicalQCRecordIDs.Contains(m.HierarchicalQCRecordID) && m.DeleteFlag != "*")
                .GroupBy(m => m.HierarchicalQCRecordID)
                .ToDictionaryAsync(m => m.Key, m => m.All(n => n.IsReadFlag.HasValue && n.IsReadFlag.Value));
        }

        /// <summary>
        /// 根据考核日期获取指定天数前的质控维护记录
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="daysAgo">多少天前</param>
        /// <param name="formType">质控模板类型</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>质控维护记录列表</returns>
        public async Task<List<HierarchicalQCMainInfo>> GetMainRecordsByDaysAgoAndFormTypeAsNoTrackAsync(string hospitalID, int daysAgo, string formType, int? departmentID = null)
        {
            var targetDate = DateTime.Now.Date.AddDays(-daysAgo - 1);

            var query = from main in _dbContext.HierarchicalQCMainInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*"
                            && ((m.AssessDate.HasValue && m.AssessDate.Value >= targetDate) || m.ModifyDateTime >= targetDate))
                        join subject in _dbContext.HierarchicalQCSubjectInfos.Where(m => m.DeleteFlag != "*" && m.FormType == formType)
                            on main.HierarchicalQCFormID equals subject.HierarchicalQCFormID
                        select main;

            if (departmentID.HasValue)
            {
                query = query.Where(m => m.DepartmentID == departmentID.Value);
            }
            return await query.OrderBy(m => m.AssessDate).AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 根据修改人员ID和时间范围批量查询质控记录
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="employeeIDs">修改人员ID列表</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>质控记录列表</returns>
        public async Task<List<HierarchicalQCMainInfo>> GetQCRecordsByEmployeeIDsAndDateRangeAsync(string hospitalID, List<string> employeeIDs, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.HierarchicalQCMainInfos
                .Where(m => m.HospitalID == hospitalID
                        && employeeIDs.Contains(m.ModifyEmployeeID)
                        && m.ModifyDateTime >= startDate
                        && m.ModifyDateTime <= endDate
                        && m.DeleteFlag != "*")
                .OrderBy(m => m.ModifyDateTime)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <summary>
        /// 根据修改人员ID和时间范围批量查询质控记录（返回视图）
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="employeeIDs">修改人员ID列表</param>
        /// <param name="startDate">开始时间</param>
        /// <returns>质控记录视图列表</returns>
        public async Task<List<QCRecordView>> GetQCRecordViewsByEmployeeIDsAndDateRangeAsync(string hospitalID, List<string> employeeIDs, DateTime startDate)
        {
            return await _dbContext.HierarchicalQCMainInfos.Where(m => 
                employeeIDs.Contains(m.ModifyEmployeeID) && m.HospitalID == hospitalID && m.ModifyDateTime >= startDate && m.DeleteFlag != "*")
                .Select(m => new QCRecordView
                {
                    ModifyEmployeeID = m.ModifyEmployeeID,
                    HierarchicalQCMainID = m.HierarchicalQCMainID,
                    HierarchicalQCRecordID = m.HierarchicalQCRecordID,
                    DepartmentID = m.DepartmentID,
                    ModifyDateTime = m.ModifyDateTime
                })
                .OrderBy(m => m.ModifyDateTime).AsNoTracking().ToListAsync();
        }
    }
}
