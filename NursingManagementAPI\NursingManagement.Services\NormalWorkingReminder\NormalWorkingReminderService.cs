﻿using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Services.NormalWorkingReminder
{
    /// <summary>
    /// 常态工作控制提醒服务实现
    /// </summary>
    public class NormalWorkingReminderService : INormalWorkingReminderService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        private readonly IProblemRectificationRepository _problemRectificationRepository;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IMessageService _messageService;

        // 常量定义
        private const string HEAD_NURSE_JOB_CODE = "975"; // 护士长职务编号
        private const string ORGANIZATION_TYPE_DISTRICT = "3"; // 片区组织类型
        private const int DISTRICT_LEVEL = 1; // 片区级别
        private const int REMINDER_TYPE_3_DAYS = 3; // 3天提醒类型
        private const int REMINDER_TYPE_6_DAYS = 6; // 6天提醒类型
        private const string NORMAL_CONTROL_FORM_TYPE = "6";


        public NormalWorkingReminderService(
            IHierarchicalQCMainRepository hierarchicalQCMainRepository,
            IProblemRectificationRepository problemRectificationRepository,
            IEmployeeToJobRepository employeeToJobRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IMessageService messageService,
            IOptions<SystemConfig> systemConfig
            )
        {
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
            _problemRectificationRepository = problemRectificationRepository;
            _employeeToJobRepository = employeeToJobRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _messageService = messageService;
            _systemConfig = systemConfig;
        }
        #region 常态过程质量控制闭环-质控问题监督提醒

        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        public async Task<ReminderResultView> ExecuteReminderAsync(ReminderRequestView requestView)
        {
            var result = new ReminderResultView();
            if (requestView == null)
            {
                result.Success = false;
                result.Message = "请求参数不能为空";
                _logger.Error("ExecuteReminderAsync: 请求参数为空");
                return result;
            }
            // 如果HospitalID为空，从配置中获取
            if (string.IsNullOrWhiteSpace(requestView.HospitalID))
            {
                requestView.HospitalID = _systemConfig.Value.HospitalID;
                if (string.IsNullOrWhiteSpace(requestView.HospitalID))
                {
                    result.Success = false;
                    result.Message = "医院ID不能为空，且配置中未找到默认医院ID";
                    _logger.Error("ExecuteReminderAsync: 医院ID为空且配置中未找到默认医院ID");
                    return result;
                }
            }
            _logger.Info($"开始执行常态工作控制提醒，医院ID：{requestView.HospitalID}，提醒类型：{requestView.ReminderType}");
            // 根据提醒类型执行不同的提醒逻辑
            switch (requestView.ReminderType)
            {
                case REMINDER_TYPE_3_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                case REMINDER_TYPE_6_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                case 0: // 全部提醒
                    return await ExecuteAllRemindersInternalAsync(requestView);
                default:
                    result.Success = false;
                    result.Message = $"不支持的提醒类型：{requestView.ReminderType}";
                    _logger.Error($"ExecuteReminderAsync: 不支持的提醒类型：{requestView.ReminderType}");
                    return result;
            }
        }

        /// <summary>
        /// 执行指定类型的提醒 - 内部通用方法
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ReminderResultView> ExecuteReminderByTypeInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            var result = new ReminderResultView();

            var reminderTypeName = reminderType == REMINDER_TYPE_3_DAYS ? "三天" : "六天";
            _logger.Info($"开始执行{reminderTypeName}未整改提醒，医院ID：{hospitalID}，部门ID：{departmentID}");
            // 获取需要提醒的问题列表
            var problems = await GetProblemsNeedReminderInternalAsync(hospitalID, reminderType, departmentID);
            if (problems == null || problems.Count == 0)
            {
                result.Success = true;
                result.Message = $"没有需要{reminderTypeName}提醒的问题";
                _logger.Info($"没有需要{reminderTypeName}提醒的问题");
                return result;
            }
            _logger.Info($"找到{problems.Count}个需要{reminderTypeName}提醒的问题");
            // 发送提醒给护士长
            if (reminderType == REMINDER_TYPE_3_DAYS)
            {
                result.ReminderDetails = await SendReminderToHeadNursesAsync(problems);
            }
            else
            {
                // 发送提醒给片区主任
                result.ReminderDetails = await SendReminderToDistrictDirectorsAsync(problems);
            }
            // 统计结果
            result.ProblemsNeedReminder = problems.Count;
            result.SuccessfulReminders = result.ReminderDetails.Count(d => d.Success);
            result.FailedReminders = result.ReminderDetails.Count(d => !d.Success);
            result.Success = result.FailedReminders == 0;
            result.Message = $"{reminderTypeName}提醒执行完成，成功：{result.SuccessfulReminders}，失败：{result.FailedReminders}";

            _logger.Info($"ExecuteReminderByTypeInternalAsync完成：{result.Message}");
            return result;

        }

        /// <summary>
        /// 执行全部提醒（三天和六天）- 内部方法
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ReminderResultView> ExecuteAllRemindersInternalAsync(ReminderRequestView requestView)
        {
            var result = new ReminderResultView();
            _logger.Info("开始执行全部提醒（三天和六天）");
            // 执行三天提醒
            var resultThreeDay = await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, REMINDER_TYPE_3_DAYS, requestView.DepartmentID);
            // 执行六天提醒
            var resultSixDay = await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, REMINDER_TYPE_6_DAYS, requestView.DepartmentID);
            // 合并结果
            result.ProblemsNeedReminder = resultThreeDay.ProblemsNeedReminder + resultSixDay.ProblemsNeedReminder;
            result.SuccessfulReminders = resultThreeDay.SuccessfulReminders + resultSixDay.SuccessfulReminders;
            result.FailedReminders = resultThreeDay.FailedReminders + resultSixDay.FailedReminders;
            result.ReminderDetails.AddRange(resultThreeDay.ReminderDetails ?? []);
            result.ReminderDetails.AddRange(resultSixDay.ReminderDetails ?? []);

            result.Success = resultThreeDay.Success && resultSixDay.Success;
            result.Message = $"全部提醒执行完成，三天提醒：{resultThreeDay.Message}，六天提醒：{resultSixDay.Message}";

            _logger.Info($"ExecuteAllRemindersAsync完成：{result.Message}");
            return result;
        }

        /// <summary>
        /// 查询未整改问题列表 - 内部方法
        /// </summary>
        /// <param name="requestView">查询请求参数</param>
        /// <returns>未整改问题列表</returns>
        private async Task<List<ReminderProblemView>> GetUnrectifiedProblemsInternalAsync(QueryUnrectifiedProblemsView requestView)
        {
            _logger.Info($"开始查询未整改问题，医院ID：{requestView.HospitalID}，最小未整改天数：{requestView.MinUnrectifiedDays}");
            // 直接从数据库获取指定天数范围的质控维护记录
            var mainRecords = await _hierarchicalQCMainRepository.GetMainRecordsByDaysAgoAndFormTypeAsNoTrackAsync(
                requestView.HospitalID, requestView.MinUnrectifiedDays,NORMAL_CONTROL_FORM_TYPE,requestView.DepartmentID);
            if (mainRecords.Count == 0)
            {
                _logger.Info("GetUnrectifiedProblemsAsync: 没有找到符合条件的质控维护记录");
                return new List<ReminderProblemView>();
            }
            // 获取这些记录的整改状态
            var mainIDs = mainRecords.Select(m => m.HierarchicalQCMainID).ToList();
            var rectificationRecords = await _problemRectificationRepository.GetProblemRectificationByHierarchicalQCMainID(mainIDs);
            var rectificationDict = rectificationRecords?.ToDictionary(r => r.HierarchicalQCMainID, r => r) ?? new Dictionary<string, ProblemRectificationView>();
            var result = new List<ReminderProblemView>();
            var currentDate = DateTime.Now.Date;

            foreach (var mainRecord in mainRecords)
            {
                if (mainRecord.AssessDate == null)
                {
                    mainRecord.AssessDate = mainRecord.ModifyDateTime;
                }
                // 检查是否已整改
                var hasRectification = rectificationDict.ContainsKey(mainRecord.HierarchicalQCMainID);
                if (hasRectification)
                {
                    continue;
                }
                // 计算未整改天数
                int unrectifiedDays = (currentDate - mainRecord.AssessDate.Value.Date).Days;
                // 只处理未整改的问题
                if (unrectifiedDays < requestView.MinUnrectifiedDays)
                {
                    continue;
                }
                // 如果设置了最大天数限制，则进行过滤
                if (requestView.MaxUnrectifiedDays.HasValue && unrectifiedDays > requestView.MaxUnrectifiedDays.Value)
                {
                    continue;
                }
                var problem = new ReminderProblemView
                {
                    HierarchicalQCMainID = mainRecord.HierarchicalQCMainID,
                    HierarchicalQCRecordID = mainRecord.HierarchicalQCRecordID,
                    ExamineDate = mainRecord.AssessDate.Value,
                    DepartmentID = mainRecord.DepartmentID,
                    Guidance = mainRecord.Guidance,
                    Improvement = mainRecord.Improvement,
                    UnrectifiedDays = unrectifiedDays,
                    RectificationDateTime = null,
                    IsRectified = hasRectification,
                    ReminderType = requestView.ReminderType,
                    ReminderTypeDescription = unrectifiedDays >= 6 ? "六天提醒片区主任" : "三天提醒护士长"
                };
                result.Add(problem);
            }
            _logger.Info($"QueryUnrectifiedProblemsAsync完成，找到{result.Count}个未整改问题");
            return result;
        }

        /// <summary>
        /// 获取需要指定类型提醒的问题列表 - 内部通用方法
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>需要提醒的问题列表</returns>
        private async Task<List<ReminderProblemView>> GetProblemsNeedReminderInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            var requestView = new QueryUnrectifiedProblemsView
            {
                HospitalID = hospitalID,
                DepartmentID = departmentID,
                ReminderType = reminderType,
            };
            // 根据提醒类型设置天数范围 // 3-5天的问题提醒护士长
            if (reminderType == REMINDER_TYPE_3_DAYS)
            {
                requestView.MinUnrectifiedDays = 3;
                requestView.MaxUnrectifiedDays = 5; 
            }
            // 6天以上的问题提醒片区主任
            else if (reminderType == REMINDER_TYPE_6_DAYS)
            {
                requestView.MinUnrectifiedDays = 6; 
                requestView.MaxUnrectifiedDays = null;
            }
            else
            {
                throw new NotImplementedException();
            }
            var problems = await GetUnrectifiedProblemsInternalAsync(requestView);
            if (problems == null || problems.Count == 0)
            {
                return [];
            }
            return problems;
        }

        /// <summary>
        /// 发送提醒消息给护士长 - 内部方法
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        private async Task<List<ReminderDetailView>> SendReminderToHeadNursesAsync(List<ReminderProblemView> problems)
        {
            var result = new List<ReminderDetailView>();
            // 按部门分组
            var problemsByDepartment = problems.GroupBy(p => p.DepartmentID).ToList();
            foreach (var departmentGroup in problemsByDepartment)
            {
                var departmentID = departmentGroup.Key;
                var departmentProblems = departmentGroup.ToList();
                if (departmentProblems.Count == 0)
                {
                    continue;
                }
                // 获取部门名称
                var departmentInfo = await GetDepartmentInfoAsync(departmentID);
                if (departmentInfo == null)
                {
                    _logger.Error($"获取部门信息失败DepartmentID={departmentID}");
                    continue;
                }
                var reminderDetail = new ReminderDetailView
                {
                    DepartmentID = departmentID,
                    ReminderType = REMINDER_TYPE_3_DAYS
                };
                // 查找护士长
                var headNurseEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, departmentID);
                if (headNurseEmployeeIDs == null || headNurseEmployeeIDs.Count == 0)
                {
                    reminderDetail.Success = false;
                    reminderDetail.ErrorMessage = $"部门{departmentID}未找到护士长";
                    _logger.Warn($"部门{departmentID}未找到护士长");
                    result.Add(reminderDetail);
                    continue;
                }
                // 构建提醒消息
                var messageContent = BuildReminderMessage(departmentProblems, REMINDER_TYPE_3_DAYS);
                // 发送消息给每个护士长
                bool allSuccess = true;
                var errorMessages = new List<string>();
                foreach (var employeeID in headNurseEmployeeIDs)
                {
                    var sendResult = await SendMessageToEmployeeAsync(employeeID, messageContent);
                    if (!sendResult)
                    {
                        allSuccess = false;
                        errorMessages.Add($"消息通知发送失败，工号{employeeID}失败，messageContent={messageContent}");
                    }
                }
                reminderDetail.DepartmentName = departmentInfo.LocalShowName;
                reminderDetail.ReceiverEmployeeIDs = headNurseEmployeeIDs;
                reminderDetail.Success = allSuccess;
                reminderDetail.Message = allSuccess ? "提醒发送成功" : "部分提醒发送失败";
                reminderDetail.ErrorMessage = errorMessages.Count > 0 ? string.Join("; ", errorMessages) : null;

                _logger.Info($"部门{departmentID}护士长提醒发送完成，成功：{allSuccess}");
                result.Add(reminderDetail);
            }
            return result;
        }

        /// <summary>
        /// 发送提醒消息给片区主任 - 内部方法
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        private async Task<List<ReminderDetailView>> SendReminderToDistrictDirectorsAsync(List<ReminderProblemView> problems)
        {
            var result = new List<ReminderDetailView>();
            // 按部门分组
            var problemsByDepartment = problems.GroupBy(p => p.DepartmentID).ToList();
            foreach (var departmentGroup in problemsByDepartment)
            {
                var departmentID = departmentGroup.Key;
                var departmentProblems = departmentGroup.ToList();
                // 获取部门名称
                var departmentInfo = await GetDepartmentInfoAsync(departmentID);
                if (departmentInfo == null)
                {
                    _logger.Error($"获取部门信息失败DepartmentID={departmentID}");
                    continue;
                }
                var reminderDetail = new ReminderDetailView
                {
                    DepartmentID = departmentID,
                    ReminderType = REMINDER_TYPE_6_DAYS
                };
                // 查找片区主任
                var districtDirectorEmployeeIDs = await FindDistrictDirectorEmployeeIDsAsync(departmentID);
                if (districtDirectorEmployeeIDs == null || districtDirectorEmployeeIDs.Count == 0)
                {
                    reminderDetail.Success = false;
                    reminderDetail.ErrorMessage = $"部门{departmentID}未找到片区主任";
                    _logger.Warn($"部门{departmentID}未找到片区主任");
                    result.Add(reminderDetail);
                    continue;
                }
                // 构建提醒消息
                var messageContent = BuildReminderMessage(departmentProblems, REMINDER_TYPE_6_DAYS);
                // 发送消息给每个片区主任
                bool allSuccess = true;
                var errorMessages = new List<string>();
                foreach (var employeeID in districtDirectorEmployeeIDs)
                {
                    var sendResult = await SendMessageToEmployeeAsync(employeeID, messageContent);
                    if (!sendResult)
                    {
                        allSuccess = false;
                        errorMessages.Add($"发送给员工{employeeID}失败");
                    }
                }
                reminderDetail.DepartmentName = departmentInfo.LocalShowName ;
                reminderDetail.ReceiverEmployeeIDs = districtDirectorEmployeeIDs;
                reminderDetail.Success = allSuccess;
                reminderDetail.Message = allSuccess ? "提醒发送成功" : "部分提醒发送失败";
                reminderDetail.ErrorMessage = errorMessages.Count > 0 ? string.Join("; ", errorMessages) : null;
                _logger.Info($"部门{departmentID}片区主任提醒发送完成，成功：{allSuccess}");

                result.Add(reminderDetail);
            }

            return result;
        }

        #region 私有辅助方法

        /// <summary>
        /// 查找片区主任员工ID列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区主任员工ID列表</returns>
        private async Task<List<string>> FindDistrictDirectorEmployeeIDsAsync(int departmentID)
        {
            // 查找上级片区部门
            var districtDepartment = await FindDistrictDepartmentAsync(departmentID);
            if (districtDepartment == null)
            {
                _logger.Warn($"部门{departmentID}未找到对应的片区部门");
                return new List<string>();
            }
            // 在片区部门中查找行政主任（这里假设片区主任也使用护士长职务编号，实际可能需要调整）
            // TODO: 需要确认片区主任的具体职务编号
            var directorEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, districtDepartment.DepartmentID);
            return directorEmployeeIDs;
        }

        /// <summary>
        /// 查找部门对应的片区部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区部门信息</returns>
        private async Task<DepartmentListInfo> FindDistrictDepartmentAsync(int departmentID)
        {
            var currentDepartmentID = departmentID;
            // 递归查找上级部门，直到找到片区（OrganizationType=3, Level=1）
            while (currentDepartmentID != 0)
            {
                var department = await GetDepartmentInfoAsync(currentDepartmentID);
                if (department == null)
                {
                    break;
                }
                // 检查是否为片区部门
                if (department.OrganizationType == ORGANIZATION_TYPE_DISTRICT && department.Level == DISTRICT_LEVEL)
                {
                    return department;
                }
                // 查找上级部门
                currentDepartmentID = department.UpperLevelDepartmentID;
            }
            return null;

        }

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>部门信息</returns>
        private async Task<DepartmentListInfo> GetDepartmentInfoAsync(int departmentID)
        {
            var allDepartments = await _departmentListRepository.GetAllDictAsync();
            return allDepartments?.FirstOrDefault(d => d.DepartmentID == departmentID);
        }

        /// <summary>
        /// 构建提醒消息内容
        /// </summary>
        /// <param name="problems">问题列表</param>
        /// <param name="reminderType">提醒类型</param>
        /// <returns>消息内容</returns>
        private string BuildReminderMessage(List<ReminderProblemView> problems, int reminderType)
        {
            var messageBuilder = new System.Text.StringBuilder();
            var reminderTypeText = $"{reminderType}天";

            messageBuilder.AppendLine($"以下是{reminderTypeText}未整改的常态工作过程控制问题：");
            for (int i = 0; i < problems.Count; i++)
            {
                var problem = problems[i];
                messageBuilder.AppendLine($"{i + 1}.问题发生人：{problem.QcEmployeeName}");
                messageBuilder.AppendLine($"发现日期：{problem.ExamineDate:yyyy-MM-dd}");
                messageBuilder.AppendLine($"未整改天数：{problem.UnrectifiedDays}天");
                messageBuilder.AppendLine();
            }
            messageBuilder.AppendLine("请及时督促相关人员进行整改。");

            return messageBuilder.ToString();

        }

        /// <summary>
        /// 发送消息给指定员工
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="content">消息内容</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendMessageToEmployeeAsync(string employeeID, string content)
        {
                if (string.IsNullOrWhiteSpace(employeeID))
                {
                    _logger.Error("SendMessageToEmployeeAsync: 员工ID为空");
                    return false;
                }
                var messageView = new MessageView
                {
                    EmployeeID = employeeID,
                    MessageCondition = new MessageConditionView
                    {
                        Message = content,
                        MessageContent = content,
                        SendMessageDateTime = DateTime.Now
                    }
                };
                var result = await _messageService.SendMessage(messageView);
                if (!result)
                {
                    _logger.Error($"发送消息给员工{employeeID}失败");
                }
                return result;
        }

        #endregion

        #endregion

        #region 管理层参与动态监测提醒

        #endregion
    }
}
