﻿using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Services.NormalWorkingReminder
{
    /// <summary>
    /// 常态工作控制提醒服务实现
    /// </summary>
    public class NormalWorkingReminderService : INormalWorkingReminderService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        private readonly IProblemRectificationRepository _problemRectificationRepository;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IMessageService _messageService;

        // 常量定义
        private const string HEAD_NURSE_JOB_CODE = "975"; // 护士长职务编号
        private const string ORGANIZATION_TYPE_DISTRICT = "3"; // 片区组织类型
        private const int DISTRICT_LEVEL = 1; // 片区级别
        private const int REMINDER_TYPE_3_DAYS = 3; // 3天提醒类型
        private const int REMINDER_TYPE_6_DAYS = 6; // 6天提醒类型
        private const string NORMAL_CONTROL_FORM_TYPE = "6";


        public NormalWorkingReminderService(
            IHierarchicalQCMainRepository hierarchicalQCMainRepository,
            IProblemRectificationRepository problemRectificationRepository,
            IEmployeeToJobRepository employeeToJobRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IMessageService messageService,
            IOptions<SystemConfig> systemConfig
            )
        {
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
            _problemRectificationRepository = problemRectificationRepository;
            _employeeToJobRepository = employeeToJobRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _messageService = messageService;
            _systemConfig = systemConfig;
        }
        #region 常态过程质量控制闭环-质控问题监督提醒

        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        public async Task<ReminderResultView> ExecuteReminderAsync(ReminderRequestView requestView)
        {
            var result = new ReminderResultView();
            if (requestView == null)
            {
                result.Success = false;
                result.Message = "请求参数不能为空";
                _logger.Error("ExecuteReminderAsync: 请求参数为空");
                return result;
            }
            // 如果HospitalID为空，从配置中获取
            if (string.IsNullOrWhiteSpace(requestView.HospitalID))
            {
                requestView.HospitalID = _systemConfig.Value.HospitalID;
                if (string.IsNullOrWhiteSpace(requestView.HospitalID))
                {
                    result.Success = false;
                    result.Message = "医院ID不能为空，且配置中未找到默认医院ID";
                    _logger.Error("ExecuteReminderAsync: 医院ID为空且配置中未找到默认医院ID");
                    return result;
                }
            }
            _logger.Info($"开始执行常态工作控制提醒，医院ID：{requestView.HospitalID}，提醒类型：{requestView.ReminderType}");
            // 根据提醒类型执行不同的提醒逻辑
            switch (requestView.ReminderType)
            {
                case REMINDER_TYPE_3_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                case REMINDER_TYPE_6_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                case 0: // 全部提醒
                    return await ExecuteAllRemindersInternalAsync(requestView);
                default:
                    result.Success = false;
                    result.Message = $"不支持的提醒类型：{requestView.ReminderType}";
                    _logger.Error($"ExecuteReminderAsync: 不支持的提醒类型：{requestView.ReminderType}");
                    return result;
            }
        }

        /// <summary>
        /// 执行指定类型的提醒 - 内部通用方法
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ReminderResultView> ExecuteReminderByTypeInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            var result = new ReminderResultView();

            var reminderTypeName = reminderType == REMINDER_TYPE_3_DAYS ? "三天" : "六天";
            _logger.Info($"开始执行{reminderTypeName}未整改提醒，医院ID：{hospitalID}，部门ID：{departmentID}");
            // 获取需要提醒的问题列表
            var problems = await GetProblemsNeedReminderInternalAsync(hospitalID, reminderType, departmentID);
            if (problems == null || problems.Count == 0)
            {
                result.Success = true;
                result.Message = $"没有需要{reminderTypeName}提醒的问题";
                _logger.Info($"没有需要{reminderTypeName}提醒的问题");
                return result;
            }
            _logger.Info($"找到{problems.Count}个需要{reminderTypeName}提醒的问题");
            // 发送提醒给护士长
            if (reminderType == REMINDER_TYPE_3_DAYS)
            {
                result.ReminderDetails = await SendReminderToHeadNursesAsync(problems);
            }
            else
            {
                // 发送提醒给片区主任
                result.ReminderDetails = await SendReminderToDistrictDirectorsAsync(problems);
            }
            // 统计结果
            result.ProblemsNeedReminder = problems.Count;
            result.SuccessfulReminders = result.ReminderDetails.Count(d => d.Success);
            result.FailedReminders = result.ReminderDetails.Count(d => !d.Success);
            result.Success = result.FailedReminders == 0;
            result.Message = $"{reminderTypeName}提醒执行完成，成功：{result.SuccessfulReminders}，失败：{result.FailedReminders}";

            _logger.Info($"ExecuteReminderByTypeInternalAsync完成：{result.Message}");
            return result;

        }

        /// <summary>
        /// 执行全部提醒（三天和六天）- 内部方法
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ReminderResultView> ExecuteAllRemindersInternalAsync(ReminderRequestView requestView)
        {
            var result = new ReminderResultView();
            _logger.Info("开始执行全部提醒（三天和六天）");
            // 执行三天提醒
            var resultThreeDay = await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, REMINDER_TYPE_3_DAYS, requestView.DepartmentID);
            // 执行六天提醒
            var resultSixDay = await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, REMINDER_TYPE_6_DAYS, requestView.DepartmentID);
            // 合并结果
            result.ProblemsNeedReminder = resultThreeDay.ProblemsNeedReminder + resultSixDay.ProblemsNeedReminder;
            result.SuccessfulReminders = resultThreeDay.SuccessfulReminders + resultSixDay.SuccessfulReminders;
            result.FailedReminders = resultThreeDay.FailedReminders + resultSixDay.FailedReminders;
            result.ReminderDetails.AddRange(resultThreeDay.ReminderDetails ?? []);
            result.ReminderDetails.AddRange(resultSixDay.ReminderDetails ?? []);

            result.Success = resultThreeDay.Success && resultSixDay.Success;
            result.Message = $"全部提醒执行完成，三天提醒：{resultThreeDay.Message}，六天提醒：{resultSixDay.Message}";

            _logger.Info($"ExecuteAllRemindersAsync完成：{result.Message}");
            return result;
        }

        /// <summary>
        /// 查询未整改问题列表 - 内部方法
        /// </summary>
        /// <param name="requestView">查询请求参数</param>
        /// <returns>未整改问题列表</returns>
        private async Task<List<ReminderProblemView>> GetUnrectifiedProblemsInternalAsync(QueryUnrectifiedProblemsView requestView)
        {
            _logger.Info($"开始查询未整改问题，医院ID：{requestView.HospitalID}，最小未整改天数：{requestView.MinUnrectifiedDays}");
            // 直接从数据库获取指定天数范围的质控维护记录
            var mainRecords = await _hierarchicalQCMainRepository.GetMainRecordsByDaysAgoAndFormTypeAsNoTrackAsync(
                requestView.HospitalID, requestView.MinUnrectifiedDays, NORMAL_CONTROL_FORM_TYPE, requestView.DepartmentID);
            if (mainRecords.Count == 0)
            {
                _logger.Info("GetUnrectifiedProblemsAsync: 没有找到符合条件的质控维护记录");
                return new List<ReminderProblemView>();
            }
            // 获取这些记录的整改状态
            var mainIDs = mainRecords.Select(m => m.HierarchicalQCMainID).ToList();
            var rectificationRecords = await _problemRectificationRepository.GetProblemRectificationByHierarchicalQCMainID(mainIDs);
            var rectificationDict = rectificationRecords?.ToDictionary(r => r.HierarchicalQCMainID, r => r) ?? new Dictionary<string, ProblemRectificationView>();
            var result = new List<ReminderProblemView>();
            var currentDate = DateTime.Now.Date;

            foreach (var mainRecord in mainRecords)
            {
                if (mainRecord.AssessDate == null)
                {
                    mainRecord.AssessDate = mainRecord.ModifyDateTime;
                }
                // 检查是否已整改
                var hasRectification = rectificationDict.ContainsKey(mainRecord.HierarchicalQCMainID);
                if (hasRectification)
                {
                    continue;
                }
                // 计算未整改天数
                int unrectifiedDays = (currentDate - mainRecord.AssessDate.Value.Date).Days;
                // 只处理未整改的问题
                if (unrectifiedDays < requestView.MinUnrectifiedDays)
                {
                    continue;
                }
                // 如果设置了最大天数限制，则进行过滤
                if (requestView.MaxUnrectifiedDays.HasValue && unrectifiedDays > requestView.MaxUnrectifiedDays.Value)
                {
                    continue;
                }
                var problem = new ReminderProblemView
                {
                    HierarchicalQCMainID = mainRecord.HierarchicalQCMainID,
                    HierarchicalQCRecordID = mainRecord.HierarchicalQCRecordID,
                    ExamineDate = mainRecord.AssessDate.Value,
                    DepartmentID = mainRecord.DepartmentID,
                    Guidance = mainRecord.Guidance,
                    Improvement = mainRecord.Improvement,
                    UnrectifiedDays = unrectifiedDays,
                    RectificationDateTime = null,
                    IsRectified = hasRectification,
                    ReminderType = requestView.ReminderType,
                    ReminderTypeDescription = unrectifiedDays >= 6 ? "六天提醒片区主任" : "三天提醒护士长"
                };
                result.Add(problem);
            }
            _logger.Info($"QueryUnrectifiedProblemsAsync完成，找到{result.Count}个未整改问题");
            return result;
        }

        /// <summary>
        /// 获取需要指定类型提醒的问题列表 - 内部通用方法
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>需要提醒的问题列表</returns>
        private async Task<List<ReminderProblemView>> GetProblemsNeedReminderInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            var requestView = new QueryUnrectifiedProblemsView
            {
                HospitalID = hospitalID,
                DepartmentID = departmentID,
                ReminderType = reminderType,
            };
            // 根据提醒类型设置天数范围 // 3-5天的问题提醒护士长
            if (reminderType == REMINDER_TYPE_3_DAYS)
            {
                requestView.MinUnrectifiedDays = 3;
                requestView.MaxUnrectifiedDays = 5;
            }
            // 6天以上的问题提醒片区主任
            else if (reminderType == REMINDER_TYPE_6_DAYS)
            {
                requestView.MinUnrectifiedDays = 6;
                requestView.MaxUnrectifiedDays = null;
            }
            else
            {
                throw new NotImplementedException();
            }
            var problems = await GetUnrectifiedProblemsInternalAsync(requestView);
            if (problems == null || problems.Count == 0)
            {
                return [];
            }
            return problems;
        }

        /// <summary>
        /// 发送提醒消息给护士长 - 内部方法
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        private async Task<List<ReminderDetailView>> SendReminderToHeadNursesAsync(List<ReminderProblemView> problems)
        {
            var result = new List<ReminderDetailView>();
            // 按部门分组
            var problemsByDepartment = problems.GroupBy(p => p.DepartmentID).ToList();
            foreach (var departmentGroup in problemsByDepartment)
            {
                var departmentID = departmentGroup.Key;
                var departmentProblems = departmentGroup.ToList();
                if (departmentProblems.Count == 0)
                {
                    continue;
                }
                // 获取部门名称
                var departmentInfo = await GetDepartmentInfoAsync(departmentID);
                if (departmentInfo == null)
                {
                    _logger.Error($"获取部门信息失败DepartmentID={departmentID}");
                    continue;
                }
                var reminderDetail = new ReminderDetailView
                {
                    DepartmentID = departmentID,
                    ReminderType = REMINDER_TYPE_3_DAYS
                };
                // 查找护士长
                var headNurseEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, departmentID);
                if (headNurseEmployeeIDs == null || headNurseEmployeeIDs.Count == 0)
                {
                    reminderDetail.Success = false;
                    reminderDetail.ErrorMessage = $"部门{departmentID}未找到护士长";
                    _logger.Warn($"部门{departmentID}未找到护士长");
                    result.Add(reminderDetail);
                    continue;
                }
                // 构建提醒消息
                var messageContent = BuildReminderMessage(departmentProblems, REMINDER_TYPE_3_DAYS);
                // 发送消息给每个护士长
                bool allSuccess = true;
                var errorMessages = new List<string>();
                foreach (var employeeID in headNurseEmployeeIDs)
                {
                    var sendResult = await SendMessageToEmployeeAsync(employeeID, messageContent);
                    if (!sendResult)
                    {
                        allSuccess = false;
                        errorMessages.Add($"消息通知发送失败，工号{employeeID}失败，messageContent={messageContent}");
                    }
                }
                reminderDetail.DepartmentName = departmentInfo.LocalShowName;
                reminderDetail.ReceiverEmployeeIDs = headNurseEmployeeIDs;
                reminderDetail.Success = allSuccess;
                reminderDetail.Message = allSuccess ? "提醒发送成功" : "部分提醒发送失败";
                reminderDetail.ErrorMessage = errorMessages.Count > 0 ? string.Join("; ", errorMessages) : null;

                _logger.Info($"部门{departmentID}护士长提醒发送完成，成功：{allSuccess}");
                result.Add(reminderDetail);
            }
            return result;
        }

        /// <summary>
        /// 发送提醒消息给片区主任 - 内部方法
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        private async Task<List<ReminderDetailView>> SendReminderToDistrictDirectorsAsync(List<ReminderProblemView> problems)
        {
            var result = new List<ReminderDetailView>();
            // 按部门分组
            var problemsByDepartment = problems.GroupBy(p => p.DepartmentID).ToList();
            foreach (var departmentGroup in problemsByDepartment)
            {
                var departmentID = departmentGroup.Key;
                var departmentProblems = departmentGroup.ToList();
                // 获取部门名称
                var departmentInfo = await GetDepartmentInfoAsync(departmentID);
                if (departmentInfo == null)
                {
                    _logger.Error($"获取部门信息失败DepartmentID={departmentID}");
                    continue;
                }
                var reminderDetail = new ReminderDetailView
                {
                    DepartmentID = departmentID,
                    ReminderType = REMINDER_TYPE_6_DAYS
                };
                // 查找片区主任
                var districtDirectorEmployeeIDs = await FindDistrictDirectorEmployeeIDsAsync(departmentID);
                if (districtDirectorEmployeeIDs == null || districtDirectorEmployeeIDs.Count == 0)
                {
                    reminderDetail.Success = false;
                    reminderDetail.ErrorMessage = $"部门{departmentID}未找到片区主任";
                    _logger.Warn($"部门{departmentID}未找到片区主任");
                    result.Add(reminderDetail);
                    continue;
                }
                // 构建提醒消息
                var messageContent = BuildReminderMessage(departmentProblems, REMINDER_TYPE_6_DAYS);
                // 发送消息给每个片区主任
                bool allSuccess = true;
                var errorMessages = new List<string>();
                foreach (var employeeID in districtDirectorEmployeeIDs)
                {
                    var sendResult = await SendMessageToEmployeeAsync(employeeID, messageContent);
                    if (!sendResult)
                    {
                        allSuccess = false;
                        errorMessages.Add($"发送给员工{employeeID}失败");
                    }
                }
                reminderDetail.DepartmentName = departmentInfo.LocalShowName;
                reminderDetail.ReceiverEmployeeIDs = districtDirectorEmployeeIDs;
                reminderDetail.Success = allSuccess;
                reminderDetail.Message = allSuccess ? "提醒发送成功" : "部分提醒发送失败";
                reminderDetail.ErrorMessage = errorMessages.Count > 0 ? string.Join("; ", errorMessages) : null;
                _logger.Info($"部门{departmentID}片区主任提醒发送完成，成功：{allSuccess}");

                result.Add(reminderDetail);
            }

            return result;
        }

        #region 私有辅助方法

        /// <summary>
        /// 查找片区主任员工ID列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区主任员工ID列表</returns>
        private async Task<List<string>> FindDistrictDirectorEmployeeIDsAsync(int departmentID)
        {
            // 查找上级片区部门
            var districtDepartment = await FindDistrictDepartmentAsync(departmentID);
            if (districtDepartment == null)
            {
                _logger.Warn($"部门{departmentID}未找到对应的片区部门");
                return new List<string>();
            }
            // 在片区部门中查找行政主任（这里假设片区主任也使用护士长职务编号，实际可能需要调整）
            // TODO: 需要确认片区主任的具体职务编号
            var directorEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, districtDepartment.DepartmentID);
            return directorEmployeeIDs;
        }

        /// <summary>
        /// 查找部门对应的片区部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区部门信息</returns>
        private async Task<DepartmentListInfo> FindDistrictDepartmentAsync(int departmentID)
        {
            var currentDepartmentID = departmentID;
            // 递归查找上级部门，直到找到片区（OrganizationType=3, Level=1）
            while (currentDepartmentID != 0)
            {
                var department = await GetDepartmentInfoAsync(currentDepartmentID);
                if (department == null)
                {
                    break;
                }
                // 检查是否为片区部门
                if (department.OrganizationType == ORGANIZATION_TYPE_DISTRICT && department.Level == DISTRICT_LEVEL)
                {
                    return department;
                }
                // 查找上级部门
                currentDepartmentID = department.UpperLevelDepartmentID;
            }
            return null;

        }

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>部门信息</returns>
        private async Task<DepartmentListInfo> GetDepartmentInfoAsync(int departmentID)
        {
            var allDepartments = await _departmentListRepository.GetAllDictAsync();
            return allDepartments?.FirstOrDefault(d => d.DepartmentID == departmentID);
        }

        /// <summary>
        /// 构建提醒消息内容
        /// </summary>
        /// <param name="problems">问题列表</param>
        /// <param name="reminderType">提醒类型</param>
        /// <returns>消息内容</returns>
        private string BuildReminderMessage(List<ReminderProblemView> problems, int reminderType)
        {
            var messageBuilder = new System.Text.StringBuilder();
            var reminderTypeText = $"{reminderType}天";

            messageBuilder.AppendLine($"以下是{reminderTypeText}未整改的常态工作过程控制问题：");
            for (int i = 0; i < problems.Count; i++)
            {
                var problem = problems[i];
                messageBuilder.AppendLine($"{i + 1}.问题发生人：{problem.QcEmployeeName}");
                messageBuilder.AppendLine($"发现日期：{problem.ExamineDate:yyyy-MM-dd}");
                messageBuilder.AppendLine($"未整改天数：{problem.UnrectifiedDays}天");
                messageBuilder.AppendLine();
            }
            messageBuilder.AppendLine("请及时督促相关人员进行整改。");

            return messageBuilder.ToString();

        }

        /// <summary>
        /// 发送消息给指定员工
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="content">消息内容</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendMessageToEmployeeAsync(string employeeID, string content)
        {
            if (string.IsNullOrWhiteSpace(employeeID))
            {
                _logger.Error("SendMessageToEmployeeAsync: 员工ID为空");
                return false;
            }
            var messageView = new MessageView
            {
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView
                {
                    Message = content,
                    MessageContent = content,
                    SendMessageDateTime = DateTime.Now
                }
            };
            var result = await _messageService.SendMessage(messageView);
            if (!result)
            {
                _logger.Error($"发送消息给员工{employeeID}失败");
            }
            return result;
        }

        #endregion

        #endregion

        #region 管理层参与动态监测提醒

        /// <summary>
        /// 执行管理层参与动态监测提醒
        /// </summary>
        /// <returns>提醒执行结果</returns>
        public async Task<ManagementParticipationReminderResult> ExecuteManagementParticipationReminderAsync()
        {
            _logger.Info("开始执行管理层参与动态监测提醒");
            // 1. 获取护士长列表（包含姓名）
            var headNurses = await GetHeadNursesWithNames();
            if (headNurses == null || headNurses.Count == 0)
            {
                return new ManagementParticipationReminderResult
                {
                    Success = false,
                    Message = "未找到护士长数据"
                };
            }
            // 2. 计算时间范围
            var timeRanges = GetTimeRangesByCurrentDate();
            // 3. 批量查询质控记录
            var employeeIDs = headNurses.Select(h => h.EmployeeID).ToList();
            var allQCRecords = await GetQCRecords(employeeIDs, timeRanges);
            // 4. 循环检查并收集提醒数据
            var reminderData = await CheckHeadNursesQC(headNurses, timeRanges, allQCRecords);
            // 5. 统一发送通知
            var result = await SendReminders(reminderData);

            _logger.Info($"管理层参与动态监测提醒执行完成，检查护士长{headNurses.Count}人，" +
                        $"一周提醒{result.WeeklyReminderCount}人，两周提醒{result.BiWeeklyReminderCount}人，月度提醒{result.MonthlyReminderCount}人");

            return result;
        }

        /// <summary>
        /// 获取护士长列表（包含姓名）
        /// </summary>
        /// <returns>护士长信息列表</returns>
        private async Task<List<HeadNurseView>> GetHeadNursesWithNames()
        {
            var headNurseJobs = await _employeeToJobRepository.GetHeadNurseJobByJobCodeAsync(HEAD_NURSE_JOB_CODE);
            if (headNurseJobs == null || headNurseJobs.Count == 0)
            {
                _logger.Info("GetHeadNursesWithNames: 未找到护士长数据");
                return null;
            }
            var employeeIDs = headNurseJobs.Select(h => h.EmployeeID).ToList();
            var personalDataList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            if (personalDataList.Count == 0)
            {
                _logger.Warn("GetHeadNursesWithNames: 未找到护士长个人信息");
                return null;
            }
            var headNurseViews = (from job in headNurseJobs
                                  join pd in personalDataList
                                 on job.EmployeeID equals pd.EmployeeID
                                  where !string.IsNullOrWhiteSpace(pd.EmployeeName)
                                  select new HeadNurseView
                                  {
                                      EmployeeID = pd.EmployeeID,
                                      EmployeeName = pd.EmployeeName,
                                      DepartmentID = job.DepartmentID
                                  }).ToList();

            _logger.Info($"GetHeadNursesWithNames: 找到{headNurseViews.Count}个护士长信息");
            return headNurseViews;
        }

        /// <summary>
        /// 计算时间范围
        /// </summary>
        /// <returns>时间范围信息</returns>
        private QCTimeRangesView GetTimeRangesByCurrentDate()
        {
            var now = DateTime.Now;
            var today = now.Date;
            // 计算上周（自然周：周一到周日）
            var dayOfWeek = (int)today.DayOfWeek;
            if (dayOfWeek == 0) dayOfWeek = 7; // 周日为7
            var lastWeekStart = today.AddDays(-(dayOfWeek + 6)); // 上周一
            var lastWeekEnd = lastWeekStart.AddDays(6); // 上周日
            // 计算上两周
            var twoWeeksAgoStart = lastWeekStart.AddDays(-7);
            // 计算当月1日到25日
            var monthStart = new DateTime(now.Year, now.Month, 1);
            var month25th = new DateTime(now.Year, now.Month, 25);
            var timeRanges = new QCTimeRangesView
            {
                LastWeekStart = lastWeekStart,
                LastWeekEnd = lastWeekEnd,
                TwoWeeksAgoStart = twoWeeksAgoStart,
                MonthStart = monthStart,
                Month25th = month25th
            };
            _logger.Info($"GetTimeRanges: 上周{lastWeekStart:yyyy-MM-dd}至{lastWeekEnd:yyyy-MM-dd}，" +
                        $"两周前{twoWeeksAgoStart:yyyy-MM-dd}，当月1-25日{monthStart:yyyy-MM-dd}至{month25th:yyyy-MM-dd}");
            return timeRanges;
        }

        /// <summary>
        /// 查询质控记录
        /// </summary>
        /// <param name="employeeIDs">员工ID列表</param>
        /// <param name="timeRanges">时间范围</param>
        /// <returns>按员工ID分组的质控记录</returns>
        private async Task<Dictionary<string, List<QCRecordView>>> GetQCRecords( List<string> employeeIDs, QCTimeRangesView timeRanges)
        {
            var hospitalID = _systemConfig.Value.HospitalID;
            var records = await _hierarchicalQCMainRepository.GetQCRecordViewsByEmployeeIDsAndDateRangeAsync(hospitalID, employeeIDs, timeRanges.TwoWeeksAgoStart);
            if (records == null)
            {
                return new Dictionary<string, List<QCRecordView>>();
            }
            //按照记录人分组
            var groupQcRecordView = records.GroupBy(m => m.ModifyEmployeeID).ToDictionary(m => m.Key, n => n.ToList());

            _logger.Info($"GetAllQCRecordsBatch: 查询到{records.Count}条质控记录，涉及{groupQcRecordView.Count}个护士长");
            return groupQcRecordView;
        }

        /// <summary>
        /// 检查护士长质控记录
        /// </summary>
        /// <param name="headNurses">护士长列表</param>
        /// <param name="timeRanges">时间范围</param>
        /// <param name="allQCRecords">所有质控记录</param>
        /// <returns>提醒收集数据</returns>
        private async Task<ReminderCollectionDataView> CheckHeadNursesQC(List<HeadNurseView> headNurses, QCTimeRangesView timeRanges, Dictionary<string, List<QCRecordView>> allQCRecords)
        {
            var reminderData = new ReminderCollectionDataView();

            foreach (var headNurse in headNurses)
            {
                if (string.IsNullOrWhiteSpace(headNurse.EmployeeID) || string.IsNullOrWhiteSpace(headNurse.EmployeeName))
                {
                    continue;
                }
                if (!allQCRecords.TryGetValue(headNurse.EmployeeID, out var nurseQCRecords))
                {
                    nurseQCRecords = new List<QCRecordView>();
                }
                // 检查一周内质控记录
                var weeklyReminder = CheckWeeklyQC(headNurse, nurseQCRecords, timeRanges);
                if (weeklyReminder != null)
                {
                    reminderData.WeeklyReminders.Add(weeklyReminder);
                }
                // 检查连续两周质控记录
                var biWeeklyReminder = await CheckBiWeeklyQC(headNurse, nurseQCRecords, timeRanges);
                if (biWeeklyReminder != null)
                {
                    reminderData.BiWeeklyReminders.Add(biWeeklyReminder);
                }
                // 检查当月25日前质控记录
                CheckMonthlyQC(headNurse, nurseQCRecords, timeRanges, reminderData.MonthlyReminders);
            }

            _logger.Info($"CheckHeadNursesQC: 一周提醒{reminderData.WeeklyReminders.Count}人，" +
                        $"两周提醒{reminderData.BiWeeklyReminders.Count}人，月度提醒{reminderData.MonthlyReminders.Count}人");

            return reminderData;
        }

        /// <summary>
        /// 检查一周质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>
        private MessageReminderView CheckWeeklyQC(HeadNurseView headNurse, List<QCRecordView> nurseQCRecords, QCTimeRangesView timeRanges)
        {
            var lastWeekRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.LastWeekStart && r.ModifyDateTime <= timeRanges.LastWeekEnd).ToList();

            if (lastWeekRecords.Count > 0)
            {
                return null;
            }
            var weeklyReminder = new MessageReminderView
            {
                EmployeeID = headNurse.EmployeeID,
                Message = $"上周（{timeRanges.LastWeekStart:M月d日}至{timeRanges.LastWeekEnd:M月d日}）还未有您的质控记录"
            };
            _logger.Debug($"CheckWeeklyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 上周无质控记录");
            return weeklyReminder;


        }

        /// <summary>
        /// 检查两周质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>

        private async Task<MessageReminderView> CheckBiWeeklyQC(
            HeadNurseView headNurse, List<QCRecordView> nurseQCRecords, QCTimeRangesView timeRanges)
        {
            var twoWeeksRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.TwoWeeksAgoStart && r.ModifyDateTime <= timeRanges.LastWeekEnd).ToList();
            if (twoWeeksRecords.Count == 0)
            {
                return null;
            }
            var districtDirectorID = await GetDistrictDirectorID(headNurse.DepartmentID);
            if (string.IsNullOrWhiteSpace(districtDirectorID))
            {
                _logger.Warn($"CheckBiWeeklyQC: 护士长{headNurse.EmployeeName}({headNurse.EmployeeID}) 未找到片区行政主任");
                return null;
            }
            var biWeeklyReminder = new MessageReminderView
            {
                EmployeeID = districtDirectorID,
                Message = $"{headNurse.EmployeeName}已连续两周无常态工作过程控制记录，请关注！"
            };
            _logger.Debug($"CheckBiWeeklyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 连续两周无质控记录");
            return biWeeklyReminder;

        }

        /// <summary>
        /// 检查月度质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>
        /// <param name="monthlyReminders">月度提醒列表</param>
        private void CheckMonthlyQC(
            HeadNurseView headNurse,
            List<QCRecordView> nurseQCRecords,
            QCTimeRangesView timeRanges,
            List<MessageReminderView> monthlyReminders)
        {
            if (DateTime.Now.Day < 25)
            {
                return;
            }
            var monthRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.MonthStart && r.ModifyDateTime <= timeRanges.Month25th).ToList();
            if (monthRecords.Count == 0)
            {
                var monthlyReminder = new MessageReminderView
                {
                    EmployeeID = headNurse.EmployeeID,
                    Message = $"{headNurse.EmployeeName}目前无常态工作过程控制记录，请关注！"
                };
                monthlyReminders.Add(monthlyReminder);
                _logger.Debug($"CheckMonthlyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 当月25日前无质控记录");
            }
        }

        /// <summary>
        /// 获取片区行政主任ID
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区行政主任ID</returns>
        private async Task<string> GetDistrictDirectorID(int departmentID)
        {
            // 查找上级片区部门
            var districtDepartment = await FindDistrictDepartment(departmentID);
            if (districtDepartment == null)
            {
                return null;
            }

            // 在片区部门中查找行政主任（使用护士长职务编号）
            var directorEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, districtDepartment.DepartmentID);
            if (directorEmployeeIDs != null && directorEmployeeIDs.Count > 0)
            {
                return directorEmployeeIDs.First();
            }

            return null;
        }

        /// <summary>
        /// 查找部门对应的片区部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区部门信息</returns>
        private async Task<DepartmentListInfo> FindDistrictDepartment(int departmentID)
        {
            var currentDepartmentID = departmentID;

            // 递归查找上级部门，直到找到片区（OrganizationType=3, Level=1）
            while (currentDepartmentID != 0)
            {
                var department = await GetDepartmentInfo(currentDepartmentID);
                if (department == null)
                {
                    break;
                }

                // 检查是否为片区部门
                if (department.OrganizationType == ORGANIZATION_TYPE_DISTRICT && department.Level == DISTRICT_LEVEL)
                {
                    return department;
                }

                // 查找上级部门
                currentDepartmentID = department.UpperLevelDepartmentID;
            }

            return null;
        }

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>部门信息</returns>
        private async Task<DepartmentListInfo> GetDepartmentInfo(int departmentID)
        {
            return await _departmentListRepository.GetByIDAsync(departmentID);
        }

        /// <summary>
        /// 统一发送提醒
        /// </summary>
        /// <param name="reminderData">提醒收集数据</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ManagementParticipationReminderResult> SendReminders(ReminderCollectionDataView reminderData)
        {
            var result = new ManagementParticipationReminderResult
            {
                WeeklyReminderCount = reminderData.WeeklyReminders.Count,
                BiWeeklyReminderCount = reminderData.BiWeeklyReminders.Count,
                MonthlyReminderCount = reminderData.MonthlyReminders.Count
            };

            var successCount = 0;
            var failCount = 0;

            // 发送一周提醒（给护士长本人）
            foreach (var reminder in reminderData.WeeklyReminders)
            {
                var success = await SendMessageToEmployee(reminder.EmployeeID, reminder.Message);
                if (success)
                {
                    successCount++;
                    result.ReminderDetails.Add($"一周提醒：{reminder.EmployeeName} - 成功");
                }
                else
                {
                    failCount++;
                    result.ReminderDetails.Add($"一周提醒：{reminder.EmployeeName} - 失败");
                }
            }

            // 发送两周提醒（给片区行政主任）
            foreach (var reminder in reminderData.BiWeeklyReminders)
            {
                var success = await SendMessageToEmployee(reminder.DistrictDirectorID, reminder.Message);
                if (success)
                {
                    successCount++;
                    result.ReminderDetails.Add($"两周提醒：{reminder.EmployeeName} -> 片区主任 - 成功");
                }
                else
                {
                    failCount++;
                    result.ReminderDetails.Add($"两周提醒：{reminder.EmployeeName} -> 片区主任 - 失败");
                }
            }

            // 发送月度提醒（给护理部李丹慧）
            const string nursingDepartmentEmployeeID = "李丹慧"; // 护理部人员ID，实际应该从配置获取
            foreach (var reminder in reminderData.MonthlyReminders)
            {
                var success = await SendMessageToEmployee(nursingDepartmentEmployeeID, reminder.Message);
                if (success)
                {
                    successCount++;
                    result.ReminderDetails.Add($"月度提醒：{reminder.EmployeeName} -> 护理部 - 成功");
                }
                else
                {
                    failCount++;
                    result.ReminderDetails.Add($"月度提醒：{reminder.EmployeeName} -> 护理部 - 失败");
                }
            }

            result.SuccessfulReminders = successCount;
            result.FailedReminders = failCount;
            result.Success = failCount == 0;
            result.Message = $"发送完成，成功{successCount}条，失败{failCount}条";

            return result;
        }

        /// <summary>
        /// 发送消息给员工
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="message">消息内容</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendMessageToEmployee(string employeeID, string message)
        {
            if (string.IsNullOrWhiteSpace(employeeID) || string.IsNullOrWhiteSpace(message))
            {
                return false;
            }

            var messageView = new MessageView
            {
                MessageTools = new List<MessageTool> { MessageTool.MQ, MessageTool.Wechat },
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView
                {
                    Type = MessageType.Notification,
                    Message = message,
                    SendMessageDateTime = DateTime.Now,
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = employeeID
                }
            };

            return await _messageService.SendMessage(messageView);
        }

        #endregion
    }
}
